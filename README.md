# Function Calling Tools - 多工具助手

基于 OpenAI Function Calling 的智能多工具助手系统，支持天气查询、翻译、摘要、数据库查询等多种工具。

## 🚀 功能特性

- **智能工具选择**: 基于 OpenAI Function Calling 自动选择合适的工具
- **多种工具支持**: 天气查询、翻译、文本摘要、数据库查询、计算器
- **现代化界面**: React + TypeScript + Tailwind CSS
- **实时交互**: 流式对话体验
- **工具管理**: 前端可视化工具 schema 管理

## 🛠️ 技术栈

### 前端
- React 18 + TypeScript
- Vite (构建工具)
- Tailwind CSS + shadcn/ui
- Zustand (状态管理)
- Axios (HTTP 客户端)

### 后端
- Node.js + Express + TypeScript
- OpenAI SDK
- 多种工具 API 集成

## 📁 项目结构

```
function-calling-tools/
├── frontend/          # React 前端应用
│   ├── src/
│   │   ├── components/    # UI 组件
│   │   ├── hooks/         # 自定义 hooks
│   │   ├── stores/        # Zustand 状态管理
│   │   ├── types/         # 类型定义
│   │   └── utils/         # 工具函数
├── backend/           # Node.js 后端 API
│   ├── src/
│   │   ├── controllers/   # 控制器
│   │   ├── services/      # 业务逻辑
│   │   ├── tools/         # 工具函数实现
│   │   └── types/         # 类型定义
├── shared/            # 前后端共享类型
└── docs/              # 项目文档
```

## 🚀 快速开始

### 环境要求
- Node.js >= 18
- npm 或 yarn
- OpenAI API Key

### 安装依赖

```bash
# 安装后端依赖
cd backend
npm install

# 安装前端依赖
cd ../frontend
npm install
```

### 环境配置

在 `backend` 目录下创建 `.env` 文件：

```env
OPENAI_API_KEY=your_openai_api_key
PORT=3001
```

### 启动项目

```bash
# 启动后端服务
cd backend
npm run dev

# 启动前端应用
cd ../frontend
npm run dev
```

## 🔧 工具说明

### 1. 天气查询工具
- 支持全球城市天气查询
- 提供温度、湿度、风速等详细信息

### 2. 翻译工具
- 支持多语言互译
- 基于 AI 的高质量翻译

### 3. 文本摘要工具
- 智能文本摘要生成
- 支持长文本压缩

### 4. 数据库查询工具
- 模拟数据库查询操作
- 支持用户、订单等数据查询

### 5. 计算器工具
- 数学表达式计算
- 支持复杂运算

## 📝 开发说明

详细的开发文档请查看 `docs/` 目录。

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📄 许可证

MIT License
