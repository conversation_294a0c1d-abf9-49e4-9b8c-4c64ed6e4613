import { Request, Response, NextFunction } from 'express';
import { ApiError } from '../../../shared/types/index';
import { ERROR_CODES, HTTP_STATUS } from '../../../shared/constants/api';

export interface AppError extends Error {
  statusCode?: number;
  code?: string;
  details?: any;
}

export function errorHandler(
  error: AppError,
  req: Request,
  res: Response,
  next: NextFunction
): void {
  console.error('Error occurred:', {
    message: error.message,
    stack: error.stack,
    url: req.url,
    method: req.method,
    body: req.body
  });

  // 默认错误响应
  let statusCode = error.statusCode || HTTP_STATUS.INTERNAL_SERVER_ERROR;
  let errorCode = error.code || ERROR_CODES.INTERNAL_SERVER_ERROR;
  let message = error.message || 'Internal server error';

  // OpenAI API 错误处理
  if (error.message.includes('OpenAI')) {
    statusCode = HTTP_STATUS.BAD_REQUEST;
    errorCode = ERROR_CODES.OPENAI_API_ERROR;
  }

  // 工具执行错误
  if (error.message.includes('Tool execution failed')) {
    statusCode = HTTP_STATUS.BAD_REQUEST;
    errorCode = ERROR_CODES.TOOL_EXECUTION_FAILED;
  }

  // 验证错误
  if (error.message.includes('validation') || error.message.includes('Invalid')) {
    statusCode = HTTP_STATUS.BAD_REQUEST;
    errorCode = ERROR_CODES.INVALID_REQUEST;
  }

  const apiError: ApiError = {
    code: errorCode,
    message,
    details: error.details
  };

  res.status(statusCode).json({
    error: apiError,
    timestamp: new Date().toISOString(),
    path: req.url
  });
}

export function createError(
  message: string,
  statusCode: number = HTTP_STATUS.INTERNAL_SERVER_ERROR,
  code?: string,
  details?: any
): AppError {
  const error = new Error(message) as AppError;
  error.statusCode = statusCode;
  error.code = code;
  error.details = details;
  return error;
}
