import { Router, Request, Response, NextFunction } from 'express';
import { ChatRequest, ChatResponse, ChatMessage } from '@shared/types/index.js';
import { OpenAIService } from '../services/openai.js';
import { executeToolCalls } from '../tools/index.js';
import { createError } from '../middleware/errorHandler.js';

const router = Router();
const openaiService = new OpenAIService();

// 内存中的对话存储（实际项目中应该使用数据库）
const conversations = new Map<string, ChatMessage[]>();

// 发送聊天消息
router.post('/', async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { message, conversation_id }: ChatRequest = req.body;

    if (!message || typeof message !== 'string') {
      throw createError('Message is required and must be a string', 400);
    }

    // 生成或获取对话ID
    const conversationId = conversation_id || `conv_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    // 获取对话历史
    const conversationHistory = conversations.get(conversationId) || [];

    // 创建用户消息
    const userMessage: ChatMessage = {
      id: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      role: 'user',
      content: message,
      timestamp: Date.now()
    };

    // 添加系统消息（如果是新对话）
    if (conversationHistory.length === 0) {
      const systemMessage: ChatMessage = {
        id: `sys_${Date.now()}`,
        role: 'system',
        content: `你是一个智能助手，可以使用多种工具来帮助用户。你有以下工具可用：
1. 天气查询 - 获取城市天气信息
2. 翻译工具 - 多语言文本翻译
3. 文本摘要 - 生成文本摘要
4. 数据库查询 - 查询用户、订单、产品等信息
5. 计算器 - 数学计算

请根据用户的需求选择合适的工具，并提供有用的回答。`,
        timestamp: Date.now()
      };
      conversationHistory.push(systemMessage);
    }

    // 添加用户消息到历史
    conversationHistory.push(userMessage);

    // 调用OpenAI获取响应
    const { message: assistantMessage, toolCalls } = await openaiService.createChatCompletion(
      conversationHistory,
      true
    );

    // 添加助手消息到历史
    conversationHistory.push(assistantMessage);

    let finalResponse = assistantMessage;

    // 如果有工具调用，执行工具并获取最终响应
    if (toolCalls && toolCalls.length > 0) {
      console.log(`🔧 Executing ${toolCalls.length} tool call(s)`);

      // 执行工具调用
      const toolResults = await executeToolCalls(toolCalls);

      // 添加工具结果到对话历史
      for (const result of toolResults) {
        const toolMessage: ChatMessage = {
          id: `tool_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          role: 'tool',
          content: result.content,
          tool_call_id: result.tool_call_id,
          timestamp: Date.now()
        };
        conversationHistory.push(toolMessage);
      }

      // 获取基于工具结果的最终响应
      const { message: finalAssistantMessage } = await openaiService.createChatCompletion(
        conversationHistory,
        false // 不再使用工具
      );

      conversationHistory.push(finalAssistantMessage);
      finalResponse = finalAssistantMessage;
    }

    // 保存对话历史
    conversations.set(conversationId, conversationHistory);

    // 构建响应
    const response: ChatResponse = {
      message: finalResponse,
      conversation_id: conversationId,
      tool_calls: toolCalls
    };

    console.log(`💬 Chat response sent for conversation: ${conversationId}`);

    res.json(response);
  } catch (error) {
    next(error);
  }
});

// 获取对话历史
router.get('/:conversationId', async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { conversationId } = req.params;
    
    const conversationHistory = conversations.get(conversationId);
    
    if (!conversationHistory) {
      throw createError('Conversation not found', 404, 'CONVERSATION_NOT_FOUND');
    }

    res.json({
      conversation_id: conversationId,
      messages: conversationHistory,
      message_count: conversationHistory.length
    });
  } catch (error) {
    next(error);
  }
});

// 删除对话
router.delete('/:conversationId', async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { conversationId } = req.params;
    
    const existed = conversations.has(conversationId);
    conversations.delete(conversationId);

    res.json({
      success: true,
      message: existed ? 'Conversation deleted' : 'Conversation not found',
      conversation_id: conversationId
    });
  } catch (error) {
    next(error);
  }
});

// 获取所有对话列表
router.get('/', async (req: Request, res: Response, next: NextFunction) => {
  try {
    const conversationList = Array.from(conversations.entries()).map(([id, messages]) => ({
      conversation_id: id,
      message_count: messages.length,
      last_message: messages[messages.length - 1],
      created_at: messages[0]?.timestamp,
      updated_at: messages[messages.length - 1]?.timestamp
    }));

    res.json({
      conversations: conversationList,
      total_count: conversationList.length
    });
  } catch (error) {
    next(error);
  }
});

export { router as chatRouter };
