import { Router, Request, Response } from 'express';
import { allTools } from '../../../shared/schemas/tools';

const router = Router();

// 健康检查端点
router.get('/', (req: Request, res: Response) => {
  const health = {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV || 'development',
    version: '1.0.0',
    services: {
      openai: !!process.env.OPENAI_API_KEY,
      database: true, // 模拟数据库总是可用
    },
    tools: {
      available: allTools.length,
      list: allTools.map(tool => ({
        name: tool.name,
        description: tool.description
      }))
    }
  };

  res.json(health);
});

// 详细系统信息
router.get('/info', (req: Request, res: Response) => {
  const info = {
    system: {
      platform: process.platform,
      arch: process.arch,
      nodeVersion: process.version,
      memory: process.memoryUsage(),
      cpuUsage: process.cpuUsage()
    },
    environment: {
      nodeEnv: process.env.NODE_ENV,
      port: process.env.PORT,
      hasOpenAIKey: !!process.env.OPENAI_API_KEY
    },
    tools: allTools
  };

  res.json(info);
});

export { router as healthRouter };
