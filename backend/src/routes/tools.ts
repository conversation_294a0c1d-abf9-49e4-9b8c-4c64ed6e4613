import { Router, Request, Response, NextFunction } from 'express';
import { ToolExecutionRequest, ToolExecutionResponse } from '../../../shared/types/index';
import { allTools } from '../../../shared/schemas/tools';
import { executeToolCall, getAvailableTools } from '../tools/index';
import { createError } from '../middleware/errorHandler';

const router = Router();

// 获取所有可用工具
router.get('/', async (req: Request, res: Response, next: NextFunction) => {
  try {
    const tools = allTools.map(tool => ({
      name: tool.name,
      description: tool.description,
      parameters: tool.parameters,
      available: true
    }));

    res.json({
      tools,
      total_count: tools.length,
      available_tools: getAvailableTools()
    });
  } catch (error) {
    next(error);
  }
});

// 获取特定工具信息
router.get('/:toolName', async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { toolName } = req.params;
    
    const tool = allTools.find(t => t.name === toolName);
    
    if (!tool) {
      throw createError(`Tool not found: ${toolName}`, 404, 'TOOL_NOT_FOUND');
    }

    res.json({
      tool,
      available: true,
      usage_examples: getToolUsageExamples(toolName)
    });
  } catch (error) {
    next(error);
  }
});

// 执行工具调用
router.post('/execute', async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { tool_call, conversation_id }: ToolExecutionRequest = req.body;

    if (!tool_call) {
      throw createError('Tool call is required', 400);
    }

    if (!tool_call.function || !tool_call.function.name) {
      throw createError('Tool function name is required', 400);
    }

    console.log(`🔧 Direct tool execution: ${tool_call.function.name}`);

    // 执行工具调用
    const result = await executeToolCall(tool_call);

    const response: ToolExecutionResponse = {
      result,
      success: true
    };

    console.log(`✅ Direct tool execution completed: ${tool_call.function.name}`);

    res.json(response);
  } catch (error: any) {
    console.error('Tool execution error:', error);
    
    const response: ToolExecutionResponse = {
      result: {
        tool_call_id: req.body.tool_call?.id || 'unknown',
        content: JSON.stringify({
          error: true,
          message: error.message,
          code: error.code || 'TOOL_EXECUTION_FAILED'
        })
      },
      success: false,
      error: error.message
    };

    res.status(error.statusCode || 500).json(response);
  }
});

// 批量执行工具调用
router.post('/execute-batch', async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { tool_calls, conversation_id } = req.body;

    if (!tool_calls || !Array.isArray(tool_calls)) {
      throw createError('Tool calls array is required', 400);
    }

    console.log(`🔧 Batch tool execution: ${tool_calls.length} tools`);

    // 执行所有工具调用
    const results = [];
    const errors = [];

    for (const toolCall of tool_calls) {
      try {
        const result = await executeToolCall(toolCall);
        results.push(result);
      } catch (error: any) {
        errors.push({
          tool_call_id: toolCall.id,
          error: error.message,
          code: error.code
        });
      }
    }

    res.json({
      results,
      errors,
      success: errors.length === 0,
      total_executed: results.length,
      total_failed: errors.length
    });
  } catch (error) {
    next(error);
  }
});

// 验证工具参数
router.post('/validate', async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { tool_call } = req.body;

    if (!tool_call) {
      throw createError('Tool call is required', 400);
    }

    const { validateToolCall } = await import('../../../shared/utils/validation');
    const validation = validateToolCall(tool_call);

    res.json({
      valid: validation.valid,
      error: validation.error,
      tool_name: tool_call.function?.name
    });
  } catch (error) {
    next(error);
  }
});

function getToolUsageExamples(toolName: string): any[] {
  const examples: Record<string, any[]> = {
    weather_query: [
      {
        description: "查询北京天气",
        arguments: { city: "北京" }
      },
      {
        description: "查询纽约天气",
        arguments: { city: "New York", country: "US" }
      }
    ],
    translate_text: [
      {
        description: "中文翻译成英文",
        arguments: { text: "你好，世界！", target_language: "en" }
      },
      {
        description: "英文翻译成日文",
        arguments: { text: "Hello, world!", target_language: "ja", source_language: "en" }
      }
    ],
    summarize_text: [
      {
        description: "生成简洁摘要",
        arguments: { 
          text: "这是一段很长的文本...", 
          style: "concise",
          max_length: 100 
        }
      }
    ],
    database_query: [
      {
        description: "查询所有用户",
        arguments: { table: "users", limit: 10 }
      },
      {
        description: "查询特定订单",
        arguments: { 
          table: "orders", 
          filters: { status: "completed" },
          limit: 5 
        }
      }
    ],
    calculate: [
      {
        description: "基本计算",
        arguments: { expression: "25 * 4 + 10" }
      },
      {
        description: "复杂计算",
        arguments: { expression: "sqrt(16) + sin(π/2)", precision: 4 }
      }
    ]
  };

  return examples[toolName] || [];
}

export { router as toolsRouter };
