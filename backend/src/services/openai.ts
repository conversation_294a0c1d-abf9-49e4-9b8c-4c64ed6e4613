import OpenAI from 'openai';
import { ChatMessage, ToolCall } from '../../../shared/types/index';
import { allTools } from '../../../shared/schemas/tools';
import { DEFAULT_CONFIG } from '../../../shared/constants/api';
import { createError } from '../middleware/errorHandler';

export class OpenAIService {
  private client: OpenAI;

  constructor() {
    // if (!process.env.OPENAI_API_KEY) {
    //   throw createError('OpenAI API key is required', 500);
    // }

    this.client = new OpenAI({
      // apiKey: process.env.OPENAI_API_KEY,
      apiKey: 'sk-holeazkksyigknkqzozszsgvgzmumdlajxjczwrecwtsdgxw',
      baseURL: 'https://api.siliconflow.cn/v1'
    });
  }

  async createChatCompletion(
    messages: ChatMessage[],
    useTools: boolean = true
  ): Promise<{
    message: ChatMessage;
    toolCalls?: ToolCall[];
  }> {
    try {
      // 转换消息格式为 OpenAI 格式
      const openaiMessages = this.convertToOpenAIMessages(messages);

      const completion = await this.client.chat.completions.create({
        model: 'gpt-3.5-turbo',
        messages: openaiMessages,
        max_tokens: DEFAULT_CONFIG.MAX_TOKENS,
        temperature: DEFAULT_CONFIG.TEMPERATURE,
        tools: useTools ? allTools.map(tool => ({ type: 'function', function: tool })) : undefined,
        tool_choice: useTools ? 'auto' : undefined,
      });

      const choice = completion.choices[0];
      if (!choice?.message) {
        throw createError('No response from OpenAI', 500);
      }

      const responseMessage: ChatMessage = {
        id: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        role: 'assistant',
        content: choice.message.content || '',
        timestamp: Date.now(),
      };

      let toolCalls: ToolCall[] | undefined;

      // 处理工具调用
      if (choice.message.tool_calls) {
        toolCalls = choice.message.tool_calls.map(tc => ({
          id: tc.id,
          type: 'function' as const,
          function: {
            name: tc.function.name,
            arguments: tc.function.arguments,
          },
        }));

        responseMessage.tool_calls = toolCalls;
      }

      return {
        message: responseMessage,
        toolCalls,
      };
    } catch (error: any) {
      console.error('OpenAI API error:', error);
      throw createError(
        `OpenAI API error: ${error.message}`,
        500,
        'OPENAI_API_ERROR',
        error
      );
    }
  }

  private convertToOpenAIMessages(messages: ChatMessage[]): OpenAI.Chat.Completions.ChatCompletionMessageParam[] {
    return messages.map(msg => {
      switch (msg.role) {
        case 'user':
          return {
            role: 'user',
            content: msg.content,
          };
        case 'assistant':
          const assistantMsg: OpenAI.Chat.Completions.ChatCompletionAssistantMessageParam = {
            role: 'assistant',
            content: msg.content,
          };
          
          if (msg.tool_calls) {
            assistantMsg.tool_calls = msg.tool_calls.map(tc => ({
              id: tc.id,
              type: 'function',
              function: {
                name: tc.function.name,
                arguments: tc.function.arguments,
              },
            }));
          }
          
          return assistantMsg;
        case 'tool':
          return {
            role: 'tool',
            content: msg.content,
            tool_call_id: msg.tool_call_id!,
          };
        case 'system':
          return {
            role: 'system',
            content: msg.content,
          };
        default:
          throw createError(`Unsupported message role: ${msg.role}`, 400);
      }
    });
  }
}
