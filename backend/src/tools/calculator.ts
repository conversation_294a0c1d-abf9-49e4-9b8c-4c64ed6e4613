import { CalculationResult } from '@shared/types/index.js';
import { DEFAULT_CONFIG } from '@shared/constants/api.js';
import { createError } from '../middleware/errorHandler.js';

interface CalculationArgs {
  expression: string;
  precision?: number;
}

export async function executeCalculation(args: CalculationArgs): Promise<CalculationResult> {
  try {
    const { expression, precision = DEFAULT_CONFIG.DEFAULT_CALCULATION_PRECISION } = args;
    
    // 清理和验证表达式
    const cleanExpression = cleanExpression(expression);
    validateExpression(cleanExpression);
    
    // 执行计算
    const { result, steps } = calculateExpression(cleanExpression);
    
    // 应用精度
    const roundedResult = Math.round(result * Math.pow(10, precision)) / Math.pow(10, precision);
    
    const calculationResult: CalculationResult = {
      expression: expression,
      result: roundedResult,
      steps
    };
    
    console.log(`🧮 Calculation: ${expression} = ${roundedResult}`);
    
    return calculationResult;
  } catch (error: any) {
    console.error('Calculation error:', error);
    throw createError(
      `Calculation failed: ${error.message}`,
      400,
      'CALCULATION_FAILED'
    );
  }
}

function cleanExpression(expression: string): string {
  // 移除空格并标准化操作符
  return expression
    .replace(/\s+/g, '')
    .replace(/×/g, '*')
    .replace(/÷/g, '/')
    .replace(/\^/g, '**')
    .toLowerCase();
}

function validateExpression(expression: string): void {
  // 检查危险字符和函数
  const dangerousPatterns = [
    /eval/i,
    /function/i,
    /import/i,
    /require/i,
    /process/i,
    /global/i,
    /window/i,
    /document/i,
    /[a-z_$][a-z0-9_$]*\s*\(/i // 防止函数调用（除了数学函数）
  ];
  
  for (const pattern of dangerousPatterns) {
    if (pattern.test(expression)) {
      throw new Error('Expression contains potentially dangerous code');
    }
  }
  
  // 检查有效字符
  const validPattern = /^[0-9+\-*/.()^√πe\s]*$/;
  const mathFunctions = ['sin', 'cos', 'tan', 'log', 'ln', 'sqrt', 'abs', 'floor', 'ceil', 'round'];
  
  let cleanedExpression = expression;
  for (const func of mathFunctions) {
    cleanedExpression = cleanedExpression.replace(new RegExp(func, 'g'), '');
  }
  
  if (!validPattern.test(cleanedExpression)) {
    throw new Error('Expression contains invalid characters');
  }
}

function calculateExpression(expression: string): { result: number; steps: string[] } {
  const steps: string[] = [];
  let currentExpression = expression;
  
  try {
    // 替换数学常数
    currentExpression = currentExpression
      .replace(/π/g, Math.PI.toString())
      .replace(/e/g, Math.E.toString());
    
    if (currentExpression !== expression) {
      steps.push(`替换常数: ${currentExpression}`);
    }
    
    // 处理数学函数
    currentExpression = processMathFunctions(currentExpression, steps);
    
    // 处理平方根
    currentExpression = currentExpression.replace(/√(\d+(?:\.\d+)?)/g, (match, num) => {
      const result = Math.sqrt(parseFloat(num));
      steps.push(`√${num} = ${result}`);
      return result.toString();
    });
    
    // 安全计算基本表达式
    const result = evaluateBasicExpression(currentExpression, steps);
    
    return { result, steps };
  } catch (error: any) {
    throw new Error(`Invalid mathematical expression: ${error.message}`);
  }
}

function processMathFunctions(expression: string, steps: string[]): string {
  const mathFunctions = {
    sin: Math.sin,
    cos: Math.cos,
    tan: Math.tan,
    log: Math.log10,
    ln: Math.log,
    sqrt: Math.sqrt,
    abs: Math.abs,
    floor: Math.floor,
    ceil: Math.ceil,
    round: Math.round
  };
  
  let result = expression;
  
  for (const [funcName, func] of Object.entries(mathFunctions)) {
    const regex = new RegExp(`${funcName}\\((\\d+(?:\\.\\d+)?)\\)`, 'g');
    result = result.replace(regex, (match, num) => {
      const value = parseFloat(num);
      const funcResult = func(value);
      steps.push(`${funcName}(${value}) = ${funcResult}`);
      return funcResult.toString();
    });
  }
  
  return result;
}

function evaluateBasicExpression(expression: string, steps: string[]): number {
  // 使用递归下降解析器安全地计算表达式
  const tokens = tokenize(expression);
  const parser = new ExpressionParser(tokens);
  const result = parser.parseExpression();
  
  if (steps.length === 0) {
    steps.push(`计算: ${expression} = ${result}`);
  } else {
    steps.push(`最终计算: ${expression} = ${result}`);
  }
  
  return result;
}

function tokenize(expression: string): string[] {
  const tokens: string[] = [];
  let current = '';
  
  for (let i = 0; i < expression.length; i++) {
    const char = expression[i];
    
    if (/[0-9.]/.test(char)) {
      current += char;
    } else {
      if (current) {
        tokens.push(current);
        current = '';
      }
      if (/[+\-*/()^]/.test(char)) {
        tokens.push(char);
      }
    }
  }
  
  if (current) {
    tokens.push(current);
  }
  
  return tokens;
}

class ExpressionParser {
  private tokens: string[];
  private position: number = 0;
  
  constructor(tokens: string[]) {
    this.tokens = tokens;
  }
  
  parseExpression(): number {
    return this.parseAddition();
  }
  
  private parseAddition(): number {
    let result = this.parseMultiplication();
    
    while (this.position < this.tokens.length) {
      const operator = this.tokens[this.position];
      if (operator === '+' || operator === '-') {
        this.position++;
        const right = this.parseMultiplication();
        result = operator === '+' ? result + right : result - right;
      } else {
        break;
      }
    }
    
    return result;
  }
  
  private parseMultiplication(): number {
    let result = this.parseExponentiation();
    
    while (this.position < this.tokens.length) {
      const operator = this.tokens[this.position];
      if (operator === '*' || operator === '/') {
        this.position++;
        const right = this.parseExponentiation();
        if (operator === '*') {
          result = result * right;
        } else {
          if (right === 0) {
            throw new Error('Division by zero');
          }
          result = result / right;
        }
      } else {
        break;
      }
    }
    
    return result;
  }
  
  private parseExponentiation(): number {
    let result = this.parseFactor();
    
    while (this.position < this.tokens.length) {
      const operator = this.tokens[this.position];
      if (operator === '^' || operator === '**') {
        this.position++;
        const right = this.parseFactor();
        result = Math.pow(result, right);
      } else {
        break;
      }
    }
    
    return result;
  }
  
  private parseFactor(): number {
    if (this.position >= this.tokens.length) {
      throw new Error('Unexpected end of expression');
    }
    
    const token = this.tokens[this.position];
    
    if (token === '(') {
      this.position++;
      const result = this.parseExpression();
      if (this.position >= this.tokens.length || this.tokens[this.position] !== ')') {
        throw new Error('Missing closing parenthesis');
      }
      this.position++;
      return result;
    }
    
    if (token === '-') {
      this.position++;
      return -this.parseFactor();
    }
    
    if (token === '+') {
      this.position++;
      return this.parseFactor();
    }
    
    const number = parseFloat(token);
    if (isNaN(number)) {
      throw new Error(`Invalid number: ${token}`);
    }
    
    this.position++;
    return number;
  }
}
