import { DatabaseQueryResult } from '@shared/types/index.js';
import { DATABASE_TABLES, DEFAULT_CONFIG } from '@shared/constants/api.js';
import { createError } from '../middleware/errorHandler.js';

interface DatabaseQueryArgs {
  table: string;
  filters?: {
    id?: number;
    name?: string;
    status?: string;
    date_range?: {
      start: string;
      end: string;
    };
  };
  limit?: number;
}

// 模拟数据库数据
const mockDatabase = {
  users: [
    { id: 1, name: '张三', email: 'zhang<PERSON>@example.com', status: 'active', created_at: '2024-01-15' },
    { id: 2, name: '李四', email: '<EMAIL>', status: 'active', created_at: '2024-02-20' },
    { id: 3, name: '王五', email: '<EMAIL>', status: 'inactive', created_at: '2024-03-10' },
    { id: 4, name: '<PERSON>', email: '<EMAIL>', status: 'active', created_at: '2024-04-05' },
    { id: 5, name: '<PERSON>', email: '<EMAIL>', status: 'pending', created_at: '2024-05-12' }
  ],
  orders: [
    { id: 101, user_id: 1, product: 'iPhone 15', amount: 999.99, status: 'completed', created_at: '2024-06-01' },
    { id: 102, user_id: 2, product: 'MacBook Pro', amount: 2499.99, status: 'pending', created_at: '2024-06-15' },
    { id: 103, user_id: 1, product: 'AirPods', amount: 199.99, status: 'completed', created_at: '2024-07-01' },
    { id: 104, user_id: 3, product: 'iPad', amount: 599.99, status: 'cancelled', created_at: '2024-07-10' },
    { id: 105, user_id: 4, product: 'Apple Watch', amount: 399.99, status: 'completed', created_at: '2024-07-12' }
  ],
  products: [
    { id: 1, name: 'iPhone 15', category: 'smartphone', price: 999.99, stock: 50, status: 'active' },
    { id: 2, name: 'MacBook Pro', category: 'laptop', price: 2499.99, stock: 25, status: 'active' },
    { id: 3, name: 'AirPods', category: 'audio', price: 199.99, stock: 100, status: 'active' },
    { id: 4, name: 'iPad', category: 'tablet', price: 599.99, stock: 30, status: 'active' },
    { id: 5, name: 'Apple Watch', category: 'wearable', price: 399.99, stock: 75, status: 'active' }
  ],
  categories: [
    { id: 1, name: 'smartphone', description: '智能手机', product_count: 15 },
    { id: 2, name: 'laptop', description: '笔记本电脑', product_count: 8 },
    { id: 3, name: 'audio', description: '音频设备', product_count: 12 },
    { id: 4, name: 'tablet', description: '平板电脑', product_count: 6 },
    { id: 5, name: 'wearable', description: '可穿戴设备', product_count: 10 }
  ]
};

export async function executeDatabaseQuery(args: DatabaseQueryArgs): Promise<DatabaseQueryResult> {
  const startTime = Date.now();
  
  try {
    const { table, filters = {}, limit = DEFAULT_CONFIG.DEFAULT_QUERY_LIMIT } = args;
    
    // 验证表名
    if (!Object.values(DATABASE_TABLES).includes(table as any)) {
      throw createError(`Invalid table name: ${table}`, 400);
    }
    
    // 获取表数据
    const tableData = mockDatabase[table as keyof typeof mockDatabase];
    if (!tableData) {
      throw createError(`Table ${table} not found`, 404);
    }
    
    // 应用过滤器
    let filteredData = applyFilters(tableData, filters);
    
    // 应用限制
    const results = filteredData.slice(0, limit);
    const totalCount = filteredData.length;
    
    const executionTime = Date.now() - startTime;
    
    const result: DatabaseQueryResult = {
      query: buildQueryDescription(table, filters, limit),
      results,
      total_count: totalCount,
      execution_time: executionTime
    };
    
    console.log(`🗄️ Database query: ${table} - ${results.length}/${totalCount} results (${executionTime}ms)`);
    
    return result;
  } catch (error: any) {
    console.error('Database query error:', error);
    throw createError(
      `Database query failed: ${error.message}`,
      500,
      'DATABASE_QUERY_FAILED'
    );
  }
}

function applyFilters(data: any[], filters: any): any[] {
  return data.filter(item => {
    // ID 过滤
    if (filters.id !== undefined && item.id !== filters.id) {
      return false;
    }
    
    // 名称过滤（模糊匹配）
    if (filters.name) {
      const nameMatch = item.name?.toLowerCase().includes(filters.name.toLowerCase()) ||
                       item.product?.toLowerCase().includes(filters.name.toLowerCase());
      if (!nameMatch) {
        return false;
      }
    }
    
    // 状态过滤
    if (filters.status && item.status !== filters.status) {
      return false;
    }
    
    // 日期范围过滤
    if (filters.date_range) {
      const itemDate = new Date(item.created_at);
      const startDate = new Date(filters.date_range.start);
      const endDate = new Date(filters.date_range.end);
      
      if (itemDate < startDate || itemDate > endDate) {
        return false;
      }
    }
    
    return true;
  });
}

function buildQueryDescription(table: string, filters: any, limit: number): string {
  let query = `SELECT * FROM ${table}`;
  
  const conditions: string[] = [];
  
  if (filters.id) {
    conditions.push(`id = ${filters.id}`);
  }
  
  if (filters.name) {
    conditions.push(`name LIKE '%${filters.name}%'`);
  }
  
  if (filters.status) {
    conditions.push(`status = '${filters.status}'`);
  }
  
  if (filters.date_range) {
    conditions.push(`created_at BETWEEN '${filters.date_range.start}' AND '${filters.date_range.end}'`);
  }
  
  if (conditions.length > 0) {
    query += ` WHERE ${conditions.join(' AND ')}`;
  }
  
  query += ` LIMIT ${limit}`;
  
  return query;
}
