import { Tool<PERSON>all, ToolCallResult } from '@shared/types/index.js';
import { validateToolCall } from '@shared/utils/validation.js';
import { createError } from '../middleware/errorHandler.js';

// 导入所有工具函数
import { executeWeatherQuery } from './weather.js';
import { executeTranslation } from './translation.js';
import { executeSummary } from './summary.js';
import { executeDatabaseQuery } from './database.js';
import { executeCalculation } from './calculator.js';

// 工具执行器映射
const toolExecutors = {
  weather_query: executeWeatherQuery,
  translate_text: executeTranslation,
  summarize_text: executeSummary,
  database_query: executeDatabaseQuery,
  calculate: executeCalculation
};

export async function executeToolCall(toolCall: ToolCall): Promise<ToolCallResult> {
  try {
    // 验证工具调用
    const validation = validateToolCall(toolCall);
    if (!validation.valid) {
      throw createError(
        `Tool validation failed: ${validation.error}`,
        400,
        'INVALID_TOOL_ARGUMENTS'
      );
    }

    const { function: func } = toolCall;
    const executor = toolExecutors[func.name as keyof typeof toolExecutors];
    
    if (!executor) {
      throw createError(
        `Unknown tool: ${func.name}`,
        400,
        'TOOL_NOT_FOUND'
      );
    }

    // 解析参数
    let args: any;
    try {
      args = JSON.parse(func.arguments);
    } catch (error) {
      throw createError(
        'Invalid JSON in function arguments',
        400,
        'INVALID_TOOL_ARGUMENTS'
      );
    }

    console.log(`🔧 Executing tool: ${func.name} with args:`, args);

    // 执行工具函数
    const result = await executor(args);

    // 构建工具调用结果
    const toolResult: ToolCallResult = {
      tool_call_id: toolCall.id,
      content: JSON.stringify(result, null, 2)
    };

    console.log(`✅ Tool execution completed: ${func.name}`);

    return toolResult;
  } catch (error: any) {
    console.error(`❌ Tool execution failed: ${toolCall.function.name}`, error);
    
    // 返回错误结果
    const errorResult: ToolCallResult = {
      tool_call_id: toolCall.id,
      content: JSON.stringify({
        error: true,
        message: error.message,
        code: error.code || 'TOOL_EXECUTION_FAILED'
      }, null, 2)
    };

    return errorResult;
  }
}

// 批量执行工具调用
export async function executeToolCalls(toolCalls: ToolCall[]): Promise<ToolCallResult[]> {
  const results: ToolCallResult[] = [];
  
  for (const toolCall of toolCalls) {
    try {
      const result = await executeToolCall(toolCall);
      results.push(result);
    } catch (error: any) {
      console.error(`Failed to execute tool ${toolCall.function.name}:`, error);
      
      // 即使单个工具失败，也要继续执行其他工具
      results.push({
        tool_call_id: toolCall.id,
        content: JSON.stringify({
          error: true,
          message: error.message,
          code: 'TOOL_EXECUTION_FAILED'
        }, null, 2)
      });
    }
  }
  
  return results;
}

// 获取所有可用工具的信息
export function getAvailableTools() {
  return Object.keys(toolExecutors).map(toolName => ({
    name: toolName,
    available: true,
    description: getToolDescription(toolName)
  }));
}

function getToolDescription(toolName: string): string {
  const descriptions = {
    weather_query: '获取指定城市的天气信息',
    translate_text: '文本翻译服务',
    summarize_text: '文本摘要生成',
    database_query: '数据库查询操作',
    calculate: '数学计算器'
  };
  
  return descriptions[toolName as keyof typeof descriptions] || 'Unknown tool';
}
