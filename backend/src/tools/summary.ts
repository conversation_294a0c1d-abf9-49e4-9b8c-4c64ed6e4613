import { SummaryResult } from '@shared/types/index.js';
import { SUMMARY_STYLES, DEFAULT_CONFIG } from '@shared/constants/api.js';
import { createError } from '../middleware/errorHandler.js';

interface SummaryArgs {
  text: string;
  max_length?: number;
  style?: string;
}

export async function executeSummary(args: SummaryArgs): Promise<SummaryResult> {
  try {
    const { text, max_length, style = SUMMARY_STYLES.CONCISE } = args;
    
    // 计算原文字数
    const originalWordCount = countWords(text);
    
    // 确定摘要长度
    const targetLength = max_length || Math.max(
      Math.floor(originalWordCount / 3),
      DEFAULT_CONFIG.DEFAULT_SUMMARY_LENGTH
    );
    
    // 生成摘要
    const summary = generateSummary(text, targetLength, style);
    const summaryWordCount = countWords(summary);
    
    const result: SummaryResult = {
      original_text: text,
      summary,
      word_count: {
        original: originalWordCount,
        summary: summaryWordCount
      }
    };
    
    console.log(`📄 Summary generated: ${originalWordCount} -> ${summaryWordCount} words (${style} style)`);
    
    return result;
  } catch (error: any) {
    console.error('Summary error:', error);
    throw createError(
      `Summary generation failed: ${error.message}`,
      500,
      'SUMMARY_FAILED'
    );
  }
}

function countWords(text: string): number {
  // 处理中英文混合文本的字数统计
  const chineseChars = (text.match(/[\u4e00-\u9fff]/g) || []).length;
  const englishWords = text
    .replace(/[\u4e00-\u9fff]/g, '') // 移除中文字符
    .trim()
    .split(/\s+/)
    .filter(word => word.length > 0).length;
  
  return chineseChars + englishWords;
}

function generateSummary(text: string, maxLength: number, style: string): string {
  // 简单的摘要生成算法（实际项目中应该使用AI模型）
  
  // 分句
  const sentences = splitIntoSentences(text);
  
  if (sentences.length <= 1) {
    return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
  }
  
  switch (style) {
    case SUMMARY_STYLES.BULLET_POINTS:
      return generateBulletPointSummary(sentences, maxLength);
    case SUMMARY_STYLES.DETAILED:
      return generateDetailedSummary(sentences, maxLength);
    case SUMMARY_STYLES.CONCISE:
    default:
      return generateConciseSummary(sentences, maxLength);
  }
}

function splitIntoSentences(text: string): string[] {
  // 分句正则，处理中英文标点
  const sentenceRegex = /[.!?。！？]+\s*/g;
  return text
    .split(sentenceRegex)
    .map(s => s.trim())
    .filter(s => s.length > 0);
}

function generateConciseSummary(sentences: string[], maxLength: number): string {
  // 选择最重要的句子（简单启发式：较长的句子通常包含更多信息）
  const scoredSentences = sentences.map(sentence => ({
    text: sentence,
    score: calculateSentenceScore(sentence)
  }));
  
  // 按分数排序
  scoredSentences.sort((a, b) => b.score - a.score);
  
  // 选择句子直到达到长度限制
  let summary = '';
  let currentLength = 0;
  
  for (const sentence of scoredSentences) {
    const sentenceLength = countWords(sentence.text);
    if (currentLength + sentenceLength <= maxLength) {
      summary += (summary ? ' ' : '') + sentence.text;
      currentLength += sentenceLength;
    }
  }
  
  return summary || sentences[0]; // 如果没有合适的句子，返回第一句
}

function generateDetailedSummary(sentences: string[], maxLength: number): string {
  // 详细摘要：保留更多信息，按原文顺序
  let summary = '';
  let currentLength = 0;
  
  for (const sentence of sentences) {
    const sentenceLength = countWords(sentence);
    if (currentLength + sentenceLength <= maxLength) {
      summary += (summary ? ' ' : '') + sentence;
      currentLength += sentenceLength;
    } else {
      break;
    }
  }
  
  return summary || sentences[0];
}

function generateBulletPointSummary(sentences: string[], maxLength: number): string {
  // 要点摘要：提取关键信息点
  const keyPoints = extractKeyPoints(sentences);
  
  let summary = '';
  let currentLength = 0;
  
  for (let i = 0; i < keyPoints.length; i++) {
    const point = `• ${keyPoints[i]}`;
    const pointLength = countWords(point);
    
    if (currentLength + pointLength <= maxLength) {
      summary += (summary ? '\n' : '') + point;
      currentLength += pointLength;
    } else {
      break;
    }
  }
  
  return summary || `• ${sentences[0]}`;
}

function extractKeyPoints(sentences: string[]): string[] {
  // 提取关键点的简单算法
  return sentences
    .map(sentence => {
      // 移除冗余词汇，提取核心信息
      return sentence
        .replace(/^(首先|其次|然后|最后|另外|此外|因此|所以|总之)/g, '')
        .replace(/^(First|Second|Then|Finally|Also|Therefore|In conclusion)/gi, '')
        .trim();
    })
    .filter(point => point.length > 10) // 过滤太短的句子
    .slice(0, 5); // 最多5个要点
}

function calculateSentenceScore(sentence: string): number {
  // 简单的句子重要性评分
  let score = 0;
  
  // 长度分数（适中长度的句子通常更重要）
  const length = countWords(sentence);
  if (length >= 10 && length <= 30) {
    score += 2;
  } else if (length >= 5 && length <= 50) {
    score += 1;
  }
  
  // 关键词分数
  const keywords = [
    '重要', '关键', '主要', '核心', '基本', '首先', '总结', '结论',
    'important', 'key', 'main', 'core', 'basic', 'first', 'summary', 'conclusion'
  ];
  
  for (const keyword of keywords) {
    if (sentence.toLowerCase().includes(keyword.toLowerCase())) {
      score += 1;
    }
  }
  
  // 数字和数据分数
  if (/\d+/.test(sentence)) {
    score += 1;
  }
  
  return score;
}
