import { TranslationResult } from '@shared/types/index.js';
import { SUPPORTED_LANGUAGES } from '@shared/constants/api.js';
import { createError } from '../middleware/errorHandler.js';

interface TranslationArgs {
  text: string;
  target_language: string;
  source_language?: string;
}

// 模拟翻译数据库 - 在实际项目中应该调用翻译API如Google Translate
const translationDatabase: Record<string, Record<string, string>> = {
  // 中文翻译
  'zh': {
    'Hello, how are you?': '你好，你好吗？',
    'Good morning': '早上好',
    'Thank you': '谢谢',
    'Goodbye': '再见',
    'I love programming': '我喜欢编程',
    'The weather is nice today': '今天天气很好',
    'What time is it?': '现在几点了？',
    'How much does this cost?': '这个多少钱？'
  },
  // 英文翻译
  'en': {
    '你好，你好吗？': 'Hello, how are you?',
    '早上好': 'Good morning',
    '谢谢': 'Thank you',
    '再见': 'Goodbye',
    '我喜欢编程': 'I love programming',
    '今天天气很好': 'The weather is nice today',
    '现在几点了？': 'What time is it?',
    '这个多少钱？': 'How much does this cost?',
    'こんにちは': 'Hello',
    'ありがとう': 'Thank you',
    '안녕하세요': 'Hello',
    '감사합니다': 'Thank you'
  },
  // 日文翻译
  'ja': {
    'Hello': 'こんにちは',
    'Thank you': 'ありがとう',
    'Good morning': 'おはよう',
    'Goodbye': 'さようなら',
    '你好': 'こんにちは',
    '谢谢': 'ありがとう'
  },
  // 韩文翻译
  'ko': {
    'Hello': '안녕하세요',
    'Thank you': '감사합니다',
    'Good morning': '좋은 아침',
    'Goodbye': '안녕히 가세요',
    '你好': '안녕하세요',
    '谢谢': '감사합니다'
  }
};

export async function executeTranslation(args: TranslationArgs): Promise<TranslationResult> {
  try {
    const { text, target_language, source_language } = args;
    
    // 检测源语言（简单的语言检测）
    const detectedSourceLang = source_language || detectLanguage(text);
    
    // 查找翻译
    let translatedText = findTranslation(text, target_language);
    
    if (!translatedText) {
      // 如果没有找到精确翻译，使用AI生成翻译（这里用简单的规则模拟）
      translatedText = generateTranslation(text, detectedSourceLang, target_language);
    }
    
    const result: TranslationResult = {
      original_text: text,
      translated_text: translatedText,
      source_language: detectedSourceLang,
      target_language: target_language
    };
    
    console.log(`🌐 Translation: ${detectedSourceLang} -> ${target_language}`);
    console.log(`📝 "${text}" -> "${translatedText}"`);
    
    return result;
  } catch (error: any) {
    console.error('Translation error:', error);
    throw createError(
      `Translation failed: ${error.message}`,
      500,
      'TRANSLATION_FAILED'
    );
  }
}

function detectLanguage(text: string): string {
  // 简单的语言检测逻辑
  const chineseRegex = /[\u4e00-\u9fff]/;
  const japaneseRegex = /[\u3040-\u309f\u30a0-\u30ff]/;
  const koreanRegex = /[\uac00-\ud7af]/;
  
  if (chineseRegex.test(text)) {
    return 'zh';
  } else if (japaneseRegex.test(text)) {
    return 'ja';
  } else if (koreanRegex.test(text)) {
    return 'ko';
  } else {
    return 'en'; // 默认为英文
  }
}

function findTranslation(text: string, targetLang: string): string | null {
  const translations = translationDatabase[targetLang];
  if (!translations) return null;
  
  // 精确匹配
  if (translations[text]) {
    return translations[text];
  }
  
  // 模糊匹配（忽略大小写和标点）
  const normalizedText = text.toLowerCase().replace(/[^\w\s]/g, '');
  for (const [key, value] of Object.entries(translations)) {
    const normalizedKey = key.toLowerCase().replace(/[^\w\s]/g, '');
    if (normalizedKey === normalizedText) {
      return value;
    }
  }
  
  return null;
}

function generateTranslation(text: string, sourceLang: string, targetLang: string): string {
  // 简单的翻译生成逻辑（实际应该调用翻译API）
  const languageNames = {
    'zh': '中文',
    'en': 'English',
    'ja': '日本語',
    'ko': '한국어',
    'fr': 'Français',
    'de': 'Deutsch',
    'es': 'Español'
  };
  
  // 如果是相同语言，直接返回原文
  if (sourceLang === targetLang) {
    return text;
  }
  
  // 生成模拟翻译
  const sourceLanguageName = languageNames[sourceLang as keyof typeof languageNames] || sourceLang;
  const targetLanguageName = languageNames[targetLang as keyof typeof languageNames] || targetLang;
  
  // 简单的翻译模拟
  if (targetLang === 'zh') {
    return `[${text}的中文翻译]`;
  } else if (targetLang === 'en') {
    return `[English translation of: ${text}]`;
  } else if (targetLang === 'ja') {
    return `[${text}の日本語翻訳]`;
  } else if (targetLang === 'ko') {
    return `[${text}의 한국어 번역]`;
  } else {
    return `[Translation from ${sourceLanguageName} to ${targetLanguageName}: ${text}]`;
  }
}
