import { ToolCall } from '../types/index';
import { toolsMap } from '../schemas/tools';
import { SUPPORTED_LANGUAGES, DATABASE_TABLES, SUMMARY_STYLES } from '../constants/api';

// 验证工具调用参数
export function validateToolCall(toolCall: ToolCall): { valid: boolean; error?: string } {
  const { function: func } = toolCall;
  const toolDef = toolsMap[func.name];
  
  if (!toolDef) {
    return { valid: false, error: `Unknown tool: ${func.name}` };
  }

  let args: any;
  try {
    args = JSON.parse(func.arguments);
  } catch (error) {
    return { valid: false, error: 'Invalid JSON in function arguments' };
  }

  // 验证必需参数
  const required = toolDef.parameters.required || [];
  for (const param of required) {
    if (!(param in args)) {
      return { valid: false, error: `Missing required parameter: ${param}` };
    }
  }

  // 根据工具类型进行特定验证
  switch (func.name) {
    case 'weather_query':
      return validateWeatherQuery(args);
    case 'translate_text':
      return validateTranslation(args);
    case 'summarize_text':
      return validateSummary(args);
    case 'database_query':
      return validateDatabaseQuery(args);
    case 'calculate':
      return validateCalculation(args);
    default:
      return { valid: true };
  }
}

function validateWeatherQuery(args: any): { valid: boolean; error?: string } {
  if (typeof args.city !== 'string' || args.city.trim().length === 0) {
    return { valid: false, error: 'City name must be a non-empty string' };
  }
  
  if (args.country && typeof args.country !== 'string') {
    return { valid: false, error: 'Country code must be a string' };
  }
  
  return { valid: true };
}

function validateTranslation(args: any): { valid: boolean; error?: string } {
  if (typeof args.text !== 'string' || args.text.trim().length === 0) {
    return { valid: false, error: 'Text must be a non-empty string' };
  }
  
  if (typeof args.target_language !== 'string') {
    return { valid: false, error: 'Target language must be a string' };
  }
  
  if (!(args.target_language in SUPPORTED_LANGUAGES)) {
    return { valid: false, error: `Unsupported target language: ${args.target_language}` };
  }
  
  if (args.source_language && !(args.source_language in SUPPORTED_LANGUAGES)) {
    return { valid: false, error: `Unsupported source language: ${args.source_language}` };
  }
  
  return { valid: true };
}

function validateSummary(args: any): { valid: boolean; error?: string } {
  if (typeof args.text !== 'string' || args.text.trim().length === 0) {
    return { valid: false, error: 'Text must be a non-empty string' };
  }
  
  if (args.max_length && (typeof args.max_length !== 'number' || args.max_length <= 0)) {
    return { valid: false, error: 'Max length must be a positive number' };
  }
  
  if (args.style && !Object.values(SUMMARY_STYLES).includes(args.style)) {
    return { valid: false, error: `Invalid summary style: ${args.style}` };
  }
  
  return { valid: true };
}

function validateDatabaseQuery(args: any): { valid: boolean; error?: string } {
  if (!Object.values(DATABASE_TABLES).includes(args.table)) {
    return { valid: false, error: `Invalid table name: ${args.table}` };
  }
  
  if (args.limit && (typeof args.limit !== 'number' || args.limit <= 0 || args.limit > 100)) {
    return { valid: false, error: 'Limit must be a number between 1 and 100' };
  }
  
  return { valid: true };
}

function validateCalculation(args: any): { valid: boolean; error?: string } {
  if (typeof args.expression !== 'string' || args.expression.trim().length === 0) {
    return { valid: false, error: 'Expression must be a non-empty string' };
  }
  
  if (args.precision && (typeof args.precision !== 'number' || args.precision < 0 || args.precision > 10)) {
    return { valid: false, error: 'Precision must be a number between 0 and 10' };
  }
  
  // 基本的表达式安全检查
  const dangerousPatterns = [
    /eval\s*\(/,
    /Function\s*\(/,
    /import\s+/,
    /require\s*\(/,
    /process\./,
    /global\./,
    /window\./
  ];
  
  for (const pattern of dangerousPatterns) {
    if (pattern.test(args.expression)) {
      return { valid: false, error: 'Expression contains potentially dangerous code' };
    }
  }
  
  return { valid: true };
}
