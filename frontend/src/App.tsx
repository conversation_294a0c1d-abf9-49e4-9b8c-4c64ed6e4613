import React, { useState } from 'react';
import { ChatInterface } from './components/ChatInterface';
import { ToolsPanel } from './components/ToolsPanel';
import { Header } from './components/Header';
import { Sidebar } from './components/Sidebar';
import { useChatStore } from './stores/chatStore';
import { useToolsStore } from './stores/toolsStore';

function App() {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [toolsPanelOpen, setToolsPanelOpen] = useState(false);
  
  const { currentConversation } = useChatStore();
  const { tools, loading: toolsLoading } = useToolsStore();

  return (
    <div className="flex h-screen bg-background">
      {/* 侧边栏 */}
      <Sidebar 
        isOpen={sidebarOpen} 
        onClose={() => setSidebarOpen(false)} 
      />

      {/* 主内容区域 */}
      <div className="flex-1 flex flex-col">
        {/* 头部 */}
        <Header 
          onToggleSidebar={() => setSidebarOpen(!sidebarOpen)}
          onToggleTools={() => setToolsPanelOpen(!toolsPanelOpen)}
        />

        {/* 内容区域 */}
        <div className="flex-1 flex overflow-hidden">
          {/* 聊天界面 */}
          <div className="flex-1 flex flex-col">
            <ChatInterface />
          </div>

          {/* 工具面板 */}
          {toolsPanelOpen && (
            <div className="w-80 border-l border-border bg-card">
              <ToolsPanel 
                tools={tools}
                loading={toolsLoading}
                onClose={() => setToolsPanelOpen(false)}
              />
            </div>
          )}
        </div>
      </div>

      {/* 移动端遮罩 */}
      {sidebarOpen && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}
    </div>
  );
}

export default App;
