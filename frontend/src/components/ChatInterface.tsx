import React, { useState, useRef, useEffect } from 'react';
import { Send, Loader2 } from 'lucide-react';
import { useChatStore } from '../stores/chatStore';
import { MessageList } from './MessageList';
import { Button } from './ui/Button';

export function ChatInterface() {
  const [inputMessage, setInputMessage] = useState('');
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  
  const { 
    currentConversation, 
    loading, 
    error, 
    sendMessage, 
    createNewConversation,
    clearError 
  } = useChatStore();

  // 自动调整文本框高度
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
    }
  }, [inputMessage]);

  // 如果没有当前对话，创建新对话
  useEffect(() => {
    if (!currentConversation) {
      createNewConversation();
    }
  }, [currentConversation, createNewConversation]);

  const handleSendMessage = async () => {
    if (!inputMessage.trim() || loading) return;

    const message = inputMessage.trim();
    setInputMessage('');
    
    try {
      await sendMessage(message);
    } catch (error) {
      console.error('Failed to send message:', error);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setInputMessage(e.target.value);
  };

  return (
    <div className="flex flex-col h-full">
      {/* 错误提示 */}
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md m-4">
          <div className="flex justify-between items-center">
            <span>{error}</span>
            <button 
              onClick={clearError}
              className="text-red-500 hover:text-red-700"
            >
              ×
            </button>
          </div>
        </div>
      )}

      {/* 消息列表 */}
      <div className="flex-1 overflow-hidden">
        <MessageList 
          messages={currentConversation?.messages || []}
          loading={loading}
        />
      </div>

      {/* 输入区域 */}
      <div className="border-t border-border bg-background p-4">
        <div className="max-w-4xl mx-auto">
          <div className="flex gap-3 items-end">
            <div className="flex-1 relative">
              <textarea
                ref={textareaRef}
                value={inputMessage}
                onChange={handleInputChange}
                onKeyPress={handleKeyPress}
                placeholder="输入您的问题，我可以帮您查询天气、翻译文本、生成摘要、查询数据或进行计算..."
                className="w-full resize-none border border-input rounded-lg px-4 py-3 pr-12 
                         focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent
                         max-h-32 min-h-[48px] bg-background text-foreground
                         placeholder:text-muted-foreground"
                rows={1}
                disabled={loading}
              />
              
              {/* 发送按钮 */}
              <Button
                onClick={handleSendMessage}
                disabled={!inputMessage.trim() || loading}
                size="sm"
                className="absolute right-2 bottom-2 h-8 w-8 p-0"
              >
                {loading ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <Send className="h-4 w-4" />
                )}
              </Button>
            </div>
          </div>
          
          {/* 提示文本 */}
          <div className="mt-2 text-xs text-muted-foreground text-center">
            按 Enter 发送，Shift + Enter 换行
          </div>
        </div>
      </div>
    </div>
  );
}
