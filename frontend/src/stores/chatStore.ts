import { create } from 'zustand';
import { ChatMessage, ConversationState, ChatResponse } from '@shared/types';
import { chatApi } from '../utils/api';

interface ChatStore {
  // 状态
  conversations: ConversationState[];
  currentConversation: ConversationState | null;
  loading: boolean;
  error: string | null;
  
  // 操作
  sendMessage: (message: string) => Promise<void>;
  createNewConversation: () => void;
  selectConversation: (conversationId: string) => void;
  deleteConversation: (conversationId: string) => void;
  loadConversations: () => Promise<void>;
  clearError: () => void;
}

export const useChatStore = create<ChatStore>((set, get) => ({
  // 初始状态
  conversations: [],
  currentConversation: null,
  loading: false,
  error: null,

  // 发送消息
  sendMessage: async (message: string) => {
    const { currentConversation } = get();
    
    set({ loading: true, error: null });

    try {
      // 创建用户消息
      const userMessage: ChatMessage = {
        id: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        role: 'user',
        content: message,
        timestamp: Date.now()
      };

      // 更新当前对话（添加用户消息）
      const updatedConversation: ConversationState = currentConversation ? {
        ...currentConversation,
        messages: [...currentConversation.messages, userMessage],
        updated_at: Date.now()
      } : {
        id: `conv_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        messages: [userMessage],
        created_at: Date.now(),
        updated_at: Date.now()
      };

      set(state => ({
        currentConversation: updatedConversation,
        conversations: currentConversation 
          ? state.conversations.map(conv => 
              conv.id === currentConversation.id ? updatedConversation : conv
            )
          : [...state.conversations, updatedConversation]
      }));

      // 发送到后端
      const response: ChatResponse = await chatApi.sendMessage({
        message,
        conversation_id: currentConversation?.id
      });

      // 更新对话（添加助手回复）
      const finalConversation: ConversationState = {
        id: response.conversation_id,
        messages: [...updatedConversation.messages, response.message],
        created_at: updatedConversation.created_at,
        updated_at: Date.now()
      };

      set(state => ({
        currentConversation: finalConversation,
        conversations: state.conversations.map(conv => 
          conv.id === response.conversation_id ? finalConversation : conv
        ),
        loading: false
      }));

    } catch (error: any) {
      console.error('Failed to send message:', error);
      set({ 
        loading: false, 
        error: error.message || '发送消息失败' 
      });
    }
  },

  // 创建新对话
  createNewConversation: () => {
    const newConversation: ConversationState = {
      id: `conv_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      messages: [],
      created_at: Date.now(),
      updated_at: Date.now()
    };

    set(state => ({
      currentConversation: newConversation,
      conversations: [newConversation, ...state.conversations]
    }));
  },

  // 选择对话
  selectConversation: (conversationId: string) => {
    const { conversations } = get();
    const conversation = conversations.find(conv => conv.id === conversationId);
    
    if (conversation) {
      set({ currentConversation: conversation });
    }
  },

  // 删除对话
  deleteConversation: async (conversationId: string) => {
    try {
      await chatApi.deleteConversation(conversationId);
      
      set(state => {
        const updatedConversations = state.conversations.filter(
          conv => conv.id !== conversationId
        );
        
        return {
          conversations: updatedConversations,
          currentConversation: state.currentConversation?.id === conversationId 
            ? null 
            : state.currentConversation
        };
      });
    } catch (error: any) {
      console.error('Failed to delete conversation:', error);
      set({ error: error.message || '删除对话失败' });
    }
  },

  // 加载对话列表
  loadConversations: async () => {
    set({ loading: true, error: null });

    try {
      const response = await chatApi.getConversations();
      
      const conversations: ConversationState[] = response.conversations.map(conv => ({
        id: conv.conversation_id,
        messages: [], // 消息会在选择对话时加载
        created_at: conv.created_at,
        updated_at: conv.updated_at
      }));

      set({ 
        conversations,
        loading: false 
      });
    } catch (error: any) {
      console.error('Failed to load conversations:', error);
      set({ 
        loading: false, 
        error: error.message || '加载对话失败' 
      });
    }
  },

  // 清除错误
  clearError: () => {
    set({ error: null });
  }
}));
