import { create } from 'zustand';
import { FunctionDefinition, ToolCall, ToolCallResult } from '@shared/types';
import { toolsApi } from '../utils/api';

interface ToolExecutionHistory {
  id: string;
  toolCall: ToolCall;
  result: ToolCallResult;
  timestamp: number;
  success: boolean;
}

interface ToolsStore {
  // 状态
  tools: FunctionDefinition[];
  loading: boolean;
  error: string | null;
  executionHistory: ToolExecutionHistory[];
  
  // 操作
  loadTools: () => Promise<void>;
  executeTool: (toolCall: ToolCall) => Promise<ToolCallResult>;
  getToolByName: (name: string) => FunctionDefinition | undefined;
  clearHistory: () => void;
  clearError: () => void;
}

export const useToolsStore = create<ToolsStore>((set, get) => ({
  // 初始状态
  tools: [],
  loading: false,
  error: null,
  executionHistory: [],

  // 加载工具列表
  loadTools: async () => {
    set({ loading: true, error: null });

    try {
      const response = await toolsApi.getTools();
      set({ 
        tools: response.tools,
        loading: false 
      });
    } catch (error: any) {
      console.error('Failed to load tools:', error);
      set({ 
        loading: false, 
        error: error.message || '加载工具失败' 
      });
    }
  },

  // 执行工具
  executeTool: async (toolCall: ToolCall): Promise<ToolCallResult> => {
    const executionId = `exec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    try {
      const response = await toolsApi.executeTool({
        tool_call: toolCall,
        conversation_id: 'direct_execution'
      });

      const historyItem: ToolExecutionHistory = {
        id: executionId,
        toolCall,
        result: response.result,
        timestamp: Date.now(),
        success: response.success
      };

      set(state => ({
        executionHistory: [historyItem, ...state.executionHistory.slice(0, 49)] // 保留最近50条
      }));

      return response.result;
    } catch (error: any) {
      console.error('Tool execution failed:', error);
      
      const errorResult: ToolCallResult = {
        tool_call_id: toolCall.id,
        content: JSON.stringify({
          error: true,
          message: error.message,
          code: 'EXECUTION_FAILED'
        })
      };

      const historyItem: ToolExecutionHistory = {
        id: executionId,
        toolCall,
        result: errorResult,
        timestamp: Date.now(),
        success: false
      };

      set(state => ({
        executionHistory: [historyItem, ...state.executionHistory.slice(0, 49)],
        error: error.message || '工具执行失败'
      }));

      throw error;
    }
  },

  // 根据名称获取工具
  getToolByName: (name: string) => {
    const { tools } = get();
    return tools.find(tool => tool.name === name);
  },

  // 清除执行历史
  clearHistory: () => {
    set({ executionHistory: [] });
  },

  // 清除错误
  clearError: () => {
    set({ error: null });
  }
}));
