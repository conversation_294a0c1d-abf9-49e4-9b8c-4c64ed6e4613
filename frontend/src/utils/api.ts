import axios from 'axios';
import { 
  ChatRequest, 
  ChatResponse, 
  ToolExecutionRequest, 
  ToolExecutionResponse,
  FunctionDefinition 
} from '@shared/types';
import { API_ENDPOINTS } from '@shared/constants/api';

// 创建axios实例
const api = axios.create({
  baseURL: import.meta.env.VITE_API_URL || 'http://localhost:3001',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    console.log(`🚀 API Request: ${config.method?.toUpperCase()} ${config.url}`);
    return config;
  },
  (error) => {
    console.error('API Request Error:', error);
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    console.log(`✅ API Response: ${response.status} ${response.config.url}`);
    return response;
  },
  (error) => {
    console.error('API Response Error:', error.response?.data || error.message);
    
    // 统一错误处理
    const errorMessage = error.response?.data?.error?.message || 
                        error.response?.data?.message || 
                        error.message || 
                        '网络请求失败';
    
    return Promise.reject(new Error(errorMessage));
  }
);

// 聊天相关API
export const chatApi = {
  // 发送消息
  sendMessage: async (request: ChatRequest): Promise<ChatResponse> => {
    const response = await api.post(API_ENDPOINTS.CHAT, request);
    return response.data;
  },

  // 获取对话历史
  getConversation: async (conversationId: string) => {
    const response = await api.get(`${API_ENDPOINTS.CHAT}/${conversationId}`);
    return response.data;
  },

  // 获取所有对话
  getConversations: async () => {
    const response = await api.get(API_ENDPOINTS.CHAT);
    return response.data;
  },

  // 删除对话
  deleteConversation: async (conversationId: string) => {
    const response = await api.delete(`${API_ENDPOINTS.CHAT}/${conversationId}`);
    return response.data;
  }
};

// 工具相关API
export const toolsApi = {
  // 获取所有工具
  getTools: async (): Promise<{ tools: FunctionDefinition[]; total_count: number }> => {
    const response = await api.get(API_ENDPOINTS.GET_TOOLS);
    return response.data;
  },

  // 获取特定工具信息
  getTool: async (toolName: string) => {
    const response = await api.get(`${API_ENDPOINTS.GET_TOOLS}/${toolName}`);
    return response.data;
  },

  // 执行工具
  executeTool: async (request: ToolExecutionRequest): Promise<ToolExecutionResponse> => {
    const response = await api.post(API_ENDPOINTS.EXECUTE_TOOL, request);
    return response.data;
  },

  // 验证工具参数
  validateTool: async (toolCall: any) => {
    const response = await api.post(`${API_ENDPOINTS.GET_TOOLS}/validate`, { tool_call: toolCall });
    return response.data;
  }
};

// 健康检查API
export const healthApi = {
  // 检查服务状态
  checkHealth: async () => {
    const response = await api.get(API_ENDPOINTS.HEALTH);
    return response.data;
  },

  // 获取系统信息
  getSystemInfo: async () => {
    const response = await api.get(`${API_ENDPOINTS.HEALTH}/info`);
    return response.data;
  }
};

// 导出默认实例
export default api;
