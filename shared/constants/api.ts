// API 端点常量
export const API_ENDPOINTS = {
  CHAT: '/api/chat',
  EXECUTE_TOOL: '/api/tools/execute',
  GET_TOOLS: '/api/tools',
  CONVERSATION: '/api/conversation',
  HEALTH: '/api/health'
} as const;

// HTTP 状态码
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  INTERNAL_SERVER_ERROR: 500
} as const;

// 错误代码
export const ERROR_CODES = {
  INVALID_REQUEST: 'INVALID_REQUEST',
  TOOL_NOT_FOUND: 'TOOL_NOT_FOUND',
  TOOL_EXECUTION_FAILED: 'TOOL_EXECUTION_FAILED',
  OPENAI_API_ERROR: 'OPENAI_API_ERROR',
  CONVERSATION_NOT_FOUND: 'CONVERSATION_NOT_FOUND',
  INVALID_TOOL_ARGUMENTS: 'INVALID_TOOL_ARGUMENTS',
  INTERNAL_SERVER_ERROR: 'INTERNAL_SERVER_ERROR'
} as const;

// 支持的语言代码
export const SUPPORTED_LANGUAGES = {
  zh: '中文',
  en: 'English', 
  ja: '日本語',
  ko: '한국어',
  fr: 'Français',
  de: 'Deutsch',
  es: 'Español',
  it: 'Italiano',
  pt: 'Português',
  ru: 'Русский'
} as const;

// 数据库表名
export const DATABASE_TABLES = {
  USERS: 'users',
  ORDERS: 'orders', 
  PRODUCTS: 'products',
  CATEGORIES: 'categories'
} as const;

// 摘要风格
export const SUMMARY_STYLES = {
  CONCISE: 'concise',
  DETAILED: 'detailed',
  BULLET_POINTS: 'bullet_points'
} as const;

// 默认配置
export const DEFAULT_CONFIG = {
  MAX_TOKENS: 1000,
  TEMPERATURE: 0.7,
  MAX_CONVERSATION_LENGTH: 50,
  DEFAULT_SUMMARY_LENGTH: 200,
  DEFAULT_CALCULATION_PRECISION: 2,
  DEFAULT_QUERY_LIMIT: 10
} as const;
