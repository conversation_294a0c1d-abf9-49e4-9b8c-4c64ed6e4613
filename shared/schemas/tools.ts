import { FunctionDefinition } from '../types/index';

// 天气查询工具
export const weatherQueryTool: FunctionDefinition = {
  name: "weather_query",
  description: "获取指定城市的当前天气信息，包括温度、湿度、风速等详细数据",
  parameters: {
    type: "object",
    properties: {
      city: {
        type: "string",
        description: "要查询天气的城市名称，支持中英文城市名"
      },
      country: {
        type: "string", 
        description: "国家代码（可选），如 CN、US、JP 等，有助于准确定位城市"
      }
    },
    required: ["city"]
  }
};

// 翻译工具
export const translateTextTool: FunctionDefinition = {
  name: "translate_text",
  description: "将文本从一种语言翻译成另一种语言，支持多种主流语言互译",
  parameters: {
    type: "object",
    properties: {
      text: {
        type: "string",
        description: "需要翻译的文本内容"
      },
      source_language: {
        type: "string",
        description: "源语言代码，如 zh、en、ja、ko 等。如果不指定，系统会自动检测"
      },
      target_language: {
        type: "string",
        description: "目标语言代码，如 zh、en、ja、ko 等"
      }
    },
    required: ["text", "target_language"]
  }
};

// 文本摘要工具
export const summarizeTextTool: FunctionDefinition = {
  name: "summarize_text",
  description: "对长文本进行智能摘要，提取关键信息和要点",
  parameters: {
    type: "object",
    properties: {
      text: {
        type: "string",
        description: "需要摘要的文本内容"
      },
      max_length: {
        type: "number",
        description: "摘要的最大字数限制，默认为原文的1/3长度"
      },
      style: {
        type: "string",
        description: "摘要风格",
        enum: ["concise", "detailed", "bullet_points"]
      }
    },
    required: ["text"]
  }
};

// 数据库查询工具
export const databaseQueryTool: FunctionDefinition = {
  name: "database_query",
  description: "查询模拟数据库中的用户、订单、产品等信息",
  parameters: {
    type: "object",
    properties: {
      table: {
        type: "string",
        description: "要查询的表名",
        enum: ["users", "orders", "products", "categories"]
      },
      filters: {
        type: "object",
        description: "查询过滤条件",
        properties: {
          id: {
            type: "number",
            description: "记录ID"
          },
          name: {
            type: "string", 
            description: "名称（支持模糊匹配）"
          },
          status: {
            type: "string",
            description: "状态"
          },
          date_range: {
            type: "object",
            description: "日期范围",
            properties: {
              start: {
                type: "string",
                description: "开始日期 (YYYY-MM-DD)"
              },
              end: {
                type: "string", 
                description: "结束日期 (YYYY-MM-DD)"
              }
            }
          }
        }
      },
      limit: {
        type: "number",
        description: "返回结果数量限制，默认10条"
      }
    },
    required: ["table"]
  }
};

// 计算器工具
export const calculateTool: FunctionDefinition = {
  name: "calculate",
  description: "执行数学计算，支持基本运算、函数运算和复杂表达式",
  parameters: {
    type: "object",
    properties: {
      expression: {
        type: "string",
        description: "数学表达式，支持 +、-、*、/、^、sqrt、sin、cos、tan、log 等运算"
      },
      precision: {
        type: "number",
        description: "计算结果的小数位数，默认为2位"
      }
    },
    required: ["expression"]
  }
};

// 导出所有工具定义
export const allTools: FunctionDefinition[] = [
  weatherQueryTool,
  translateTextTool,
  summarizeTextTool,
  databaseQueryTool,
  calculateTool
];

// 工具映射表
export const toolsMap: Record<string, FunctionDefinition> = {
  weather_query: weatherQueryTool,
  translate_text: translateTextTool,
  summarize_text: summarizeTextTool,
  database_query: databaseQueryTool,
  calculate: calculateTool
};
